# 分段请求修复验证

## 问题描述
- cmd: 10 (设备配置) 有分段请求
- cmd: 4 (设备状态) 没有分段请求
- 两者应该使用相同的字段集合，都基于 `supports` 确定

## 根本原因
1. **字段集合不一致**：
   - 配置请求使用：`["deviceName", "led"]` (基础字段，不触发分段)
   - 状态请求使用：`["userList"]` (可能分段，但数据量小)

2. **按需加载策略**：
   - 大数据字段（swPort、swPoe等）只在特定标签页访问时加载
   - 导致配置和状态请求在不同时机请求不同字段

## 修复方案
修改 `loadInitialData` 函数：
1. 从 `supports` 中获取所有支持的字段
2. 配置和状态请求使用相同的字段集合
3. 确保两者有相同的分段行为

## 修复后的行为
- 配置和状态请求都会基于 `supports` 请求相同字段
- 如果字段需要分段（如 swPort、swPoe），两者都会分段
- 保证了请求行为的一致性

## 验证方法
1. 打开设备配置抽屉
2. 观察网络请求日志
3. 确认 cmd: 10 和 cmd: 4 都有相同的分段行为
4. 检查请求的字段集合是否一致

## 预期结果
- cmd: 10 和 cmd: 4 都应该有分段请求
- 或者都没有分段请求（取决于 supports 中的字段）
- 两者的分段行为应该完全一致
